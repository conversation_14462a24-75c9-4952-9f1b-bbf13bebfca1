import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { ScrollView } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import {
  ArrowLeft,
  Search,
  SlidersHorizontal,
  ChevronRight,
} from "lucide-react-native";

interface ProgramListItemProps {
  title: string;
  onPress?: () => void;
}

const ProgramListItem: React.FC<ProgramListItemProps> = ({ title, onPress }) => {
  return (
    <Pressable
      onPress={onPress}
      className="flex-row items-center justify-between px-4 py-4 bg-white active:bg-background-50"
    >
      <Text className="text-base font-dm-sans-regular text-typography-700 flex-1">
        {title}
      </Text>
      <Icon
        as={ChevronRight}
        size="md"
        className="text-typography-400"
      />
    </Pressable>
  );
};

const ProgramDetails = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const params = useLocalSearchParams();
  const programTitle = params.title as string || "Other programs";

  const handleBack = () => {
    router.back();
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
  };

  const handleItemPress = (item: string) => {
    console.log(`Pressed: ${item}`);
    // TODO: Implement navigation for each program item
  };

  // Program items based on the design
  const programItems = [
    "Before & after school care",
    "Community outreach", 
    "Day camp & child care",
    "Sleep away camp",
    "Teen programs",
    "Volunteer",
    "Website",
    "Youth sports",
  ];

  const filteredItems = programItems.filter(item =>
    item.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white items-center justify-between">
          <HStack className="items-center" space="md">
            <Pressable onPress={handleBack} className="p-1">
              <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
            </Pressable>
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              {programTitle}
            </Text>
          </HStack>
        </HStack>

        {/* Search */}
        <VStack className="px-4 pb-4 bg-white">
          <Input variant="outline" className="bg-background-50 rounded-xl" size="lg">
            <InputSlot>
              <InputIcon as={Search} className="text-typography-400 ml-3" />
            </InputSlot>
            <InputField
              placeholder="Search"
              className="placeholder:text-typography-400"
              onChangeText={handleSearch}
              value={searchTerm}
            />
            <InputSlot>
              <Pressable className="p-2 bg-background-100 rounded-lg mr-2 border border-background-200">
                <Icon as={SlidersHorizontal} size="sm" className="text-typography-600" />
              </Pressable>
            </InputSlot>
          </Input>
        </VStack>

        <ScrollView 
          className="flex-1" 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 16 }}
        >
          {/* Program List */}
          <VStack className="bg-white rounded-2xl mx-4 overflow-hidden shadow-sm">
            {filteredItems.map((item, index) => (
              <VStack key={item}>
                <ProgramListItem
                  title={item}
                  onPress={() => handleItemPress(item)}
                />
                {index < filteredItems.length - 1 && (
                  <VStack className="h-px bg-background-100 mx-4" />
                )}
              </VStack>
            ))}
          </VStack>

          {filteredItems.length === 0 && searchTerm && (
            <VStack className="items-center justify-center py-8">
              <Text className="text-typography-500 font-dm-sans-regular">
                No programs found matching "{searchTerm}"
              </Text>
            </VStack>
          )}
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default ProgramDetails;
