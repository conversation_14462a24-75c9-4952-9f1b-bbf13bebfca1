import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { SafeAreaView } from "react-native-safe-area-context";
import { FlatList } from "react-native";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "lucide-react-native";
import { router, useLocalSearchParams, useRouter } from "expo-router";
import { TrainerCard } from "@/components/screens/trainers/trainer-card";
import { useTrainersQueryBySession } from "@/data/screens/appointments/queries/useTrainersQueryBySession";

const TrainersHeader = () => {
  return (
    <HStack className="items-center px-4 py-4 bg-white" space="md">
      <Pressable
        onPress={() => router.back()}
        className="w-10 h-10 items-center justify-center rounded-full bg-background-100"
      >
        <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
      </Pressable>

      <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
        Trainers
      </Text>
    </HStack>
  );
};

const Trainers = () => {
  const { sessionId } = useLocalSearchParams<{ sessionId: string }>();

  const { data } = useTrainersQueryBySession(sessionId);

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        <TrainersHeader />

        <VStack className="flex-1 px-4 pt-2">
          <FlatList
            data={data}
            renderItem={({ item }) => <TrainerCard key={item.id} {...item} />}
            keyExtractor={(item) => String(item.id)}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingBottom: 20,
              paddingTop: 8,
            }}
          />
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Trainers;
