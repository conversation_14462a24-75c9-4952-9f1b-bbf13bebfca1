import React from "react";
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { useToaster } from "@/components/screens/classes/class-card/toast";
import { uniqueId } from "lodash/fp";

export const ToastTest = () => {
  const toast = useToaster(uniqueId("test"));

  const testErrorToast = () => {
    toast("This is a test error message to verify the alert system is working!");
  };

  const testEmptyToast = () => {
    toast();
  };

  const testUndefinedToast = () => {
    toast(undefined);
  };

  return (
    <VStack space="md" className="p-4">
      <Text className="text-lg font-dm-sans-bold">Toast Alert Test</Text>
      
      <Button onPress={testErrorToast} className="bg-red-500">
        <ButtonText>Test Error Toast</ButtonText>
      </Button>
      
      <Button onPress={testEmptyToast} className="bg-orange-500">
        <ButtonText>Test Empty Message Toast</ButtonText>
      </Button>
      
      <Button onPress={testUndefinedToast} className="bg-yellow-500">
        <ButtonText>Test Undefined Message Toast</ButtonText>
      </Button>
    </VStack>
  );
};
