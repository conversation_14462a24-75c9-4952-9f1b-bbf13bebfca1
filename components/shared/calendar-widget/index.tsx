import React, { useState } from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Box } from "@/components/ui/box";
import { ChevronLeft, ChevronRight } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import {
  getDaysInMonth,
  getDay,
  startOfMonth,
  addMonths,
  subMonths,
  isToday,
  isSameDay,
  format,
} from "date-fns";

interface CalendarWidgetProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const CalendarWidget: React.FC<CalendarWidgetProps> = ({
  selectedDate,
  onDateSelect,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const dayNames = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getDay(startOfMonth(currentMonth));
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const isTodayDate = (day: number) => {
    const dayDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return isToday(dayDate);
  };

  const isSelectedDate = (day: number) => {
    const dayDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return isSameDay(dayDate, selectedDate);
  };

  const handleDatePress = (day: number) => {
    const newDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    onDateSelect(newDate);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    if (direction === "prev") {
      setCurrentMonth(subMonths(currentMonth, 1));
    } else {
      setCurrentMonth(addMonths(currentMonth, 1));
    }
  };

  const calendarDays = generateCalendarDays();

  return (
    <VStack
      space="sm"
      className="mx-4 p-3 bg-white rounded-2xl border border-background-200"
    >
      {/* Header */}
      <HStack className="justify-between items-center">
        <Text className="text-base font-dm-sans-bold text-typography-900">
          {format(currentMonth, "MMMM yyyy")}
        </Text>
        <HStack space="xs">
          <Pressable
            onPress={() => navigateMonth("prev")}
            className="p-1 rounded-full"
          >
            <Icon as={ChevronLeft} size="sm" className="text-typography-600" />
          </Pressable>
          <Pressable
            onPress={() => navigateMonth("next")}
            className="p-1 rounded-full"
          >
            <Icon as={ChevronRight} size="sm" className="text-typography-600" />
          </Pressable>
        </HStack>
      </HStack>

      {/* Day names */}
      <HStack className="justify-between">
        {dayNames.map((dayName) => (
          <Box key={dayName} className="w-8 items-center">
            <Text className="text-xs font-dm-sans-medium text-typography-600">
              {dayName}
            </Text>
          </Box>
        ))}
      </HStack>

      {/* Calendar grid */}
      <VStack space="xs">
        {Array.from(
          { length: Math.ceil(calendarDays.length / 7) },
          (_, weekIndex) => (
            <HStack key={weekIndex} className="justify-between">
              {calendarDays
                .slice(weekIndex * 7, (weekIndex + 1) * 7)
                .map((day, dayIndex) => (
                  <Box
                    key={dayIndex}
                    className="w-8 h-8 items-center justify-center"
                  >
                    {day && (
                      <Pressable
                        onPress={() => handleDatePress(day)}
                        className={`w-7 h-7 rounded-lg items-center justify-center ${
                          isSelectedDate(day)
                            ? "bg-[#00BFE0]"
                            : isTodayDate(day)
                            ? "bg-[#00BFE0]/10"
                            : ""
                        }`}
                      >
                        <Text
                          className={`text-xs font-dm-sans-medium ${
                            isSelectedDate(day)
                              ? "text-white"
                              : isTodayDate(day)
                              ? "text-[#00BFE0]"
                              : "text-typography-900"
                          }`}
                        >
                          {day}
                        </Text>
                      </Pressable>
                    )}
                  </Box>
                ))}
            </HStack>
          )
        )}
      </VStack>
    </VStack>
  );
};

export default CalendarWidget;
