import React, { useState, useRef, useEffect } from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import { CalendarDays } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import CalendarWidget from "@/components/shared/calendar-widget";
import { Modal, View } from "react-native";
import {
  format,
  isToday as isTodayFn,
  isSameDay,
  addDays,
  startOfDay,
} from "date-fns";
import { Box } from "@/components/ui/box";

interface HorizontalDatePickerProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const generateDates = () =>
  Array.from({ length: 60 }, (_, i) => addDays(startOfDay(new Date()), i));

const HorizontalDatePicker = ({
  selectedDate,
  onDateSelect,
}: HorizontalDatePickerProps) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const dates = generateDates();

  const selectedIndex = dates.findIndex((date) =>
    isSameDay(date, selectedDate)
  );

  useEffect(() => {
    let clearTimeoutId: NodeJS.Timeout | null = null;

    if (selectedIndex !== -1 && scrollViewRef.current) {
      clearTimeoutId = setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          x: selectedIndex * 40, // Approximate width of each date item (48px + spacing)
          animated: true,
        });
      }, 100);
    }

    return () => {
      if (clearTimeoutId) {
        clearTimeout(clearTimeoutId);
      }
    };
  }, [selectedDate]);

  const formatDate = (date: Date) => {
    const dayName = format(date, "EEEEEE").toUpperCase();
    const dayNumber = format(date, "d");
    return { dayName, dayNumber };
  };

  const isSelected = (date: Date) => {
    return isSameDay(date, selectedDate);
  };

  const handleDatePress = (date: Date) => {
    onDateSelect(date);
  };

  const handleCalendarSelect = (date: Date) => {
    onDateSelect(date);
    setShowCalendar(false);
  };

  return (
    <>
      <HStack className="items-center px-4 py-3" space="md">
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          className="flex-1"
          contentContainerStyle={{ paddingHorizontal: 8 }}
        >
          <HStack space="sm">
            {dates.map((date, index) => {
              const { dayName, dayNumber } = formatDate(date);
              const selected = isSelected(date);
              const today = isTodayFn(date);

              return (
                <Pressable
                  key={index}
                  onPress={() => handleDatePress(date)}
                  className="items-center"
                >
                  <VStack
                    className={`items-center text-xs justify-center px-3 py-3 max-w-[48px] max-h-[70px] ${
                      selected
                        ? "bg-[#00BFE0] rounded-full"
                        : "bg-transparent border border-background-200 rounded-full"
                    }`}
                    space="xs"
                  >
                    <Text
                      size="xs"
                      className={`text-xs  ${
                        selected ? "text-black" : today ? "" : ""
                      }`}
                    >
                      {dayName}
                    </Text>
                    <View className="flex-1 items-center justify-center gap-1">
                      <Text
                        size="xs"
                        className={`text-xs font-dm-sans-bold ${
                          selected ? "text-black" : "text-typography-900"
                        }`}
                      >
                        {dayNumber}
                      </Text>
                      {selected && (
                        <Box className="w-1 h-1 bg-black rounded-full" />
                      )}
                    </View>
                  </VStack>
                </Pressable>
              );
            })}
          </HStack>
        </ScrollView>

        <Pressable
          onPress={() => setShowCalendar(true)}
          className="p-3 bg-white rounded-xl border border-background-200 rounded-full"
        >
          <Icon as={CalendarDays} size="lg" color="black" />
        </Pressable>
      </HStack>

      <Modal
        visible={showCalendar}
        transparent
        animationType="fade"
        onRequestClose={() => setShowCalendar(false)}
      >
        <Pressable
          className="flex-1 bg-black/50 justify-center items-center"
          onPress={() => setShowCalendar(false)}
        >
          <Pressable onPress={(e) => e.stopPropagation()}>
            <CalendarWidget
              selectedDate={selectedDate}
              onDateSelect={handleCalendarSelect}
            />
          </Pressable>
        </Pressable>
      </Modal>
    </>
  );
};

export default HorizontalDatePicker;
