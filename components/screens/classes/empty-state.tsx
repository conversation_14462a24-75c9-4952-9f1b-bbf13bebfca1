import React, { ReactNode } from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";

import { EmptyClassesIcon } from "@/components/shared/icon/empty-class";

import { ReactElement } from "react";

interface EmptyStateProps {
  icon?: ReactElement;
  title?: string;
  subtitle?: string;
  action?: ReactElement;
}

export const EmptyState = ({
  title = "There are no classes available",
  subtitle = "Click on another date to see more classes",
  icon = <EmptyClassesIcon />,
  action,
}: EmptyStateProps) => {
  const clonedIcon = React.cloneElement(icon, {
    className: "mb-6 w-24 h-24 mt-7",
  });

  return (
    <VStack className=" justify-center items-center px-4 py-8 ">
      {clonedIcon}

      <Text className="text-lg font-dm-sans-medium text-typography-900 text-center mb-2">
        {title}
      </Text>

      <Text className="text-sm font-dm-sans-regular text-typography-600 text-center">
        {subtitle}
      </Text>
      {action}
    </VStack>
  );
};
