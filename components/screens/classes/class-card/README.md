# Enhanced Action Button Component

This directory contains an enhanced action button component that supports multiple button states for class reservations, as shown in your design mockup.

## Components

### 1. `StatusButton` (class-button.tsx)
The main action button component with support for 7 different states:

#### Button States

| State | Description | Visual Style | Interactive |
|-------|-------------|--------------|-------------|
| `available` | Class has spots available | Light blue background | ✅ |
| `cancel_reservation` | User can cancel their reservation | Light red background with red border | ✅ |
| `class_cancelled` | Class has been cancelled | Gray background (disabled) | ❌ |
| `waitlist` | Join waitlist available | Light green background with green border | ✅ |
| `walk_in_available` | Walk-in spots available | White background with gray border | ✅ |
| `class_full` | Class is completely full | Gray background (disabled) | ❌ |
| `reserved` | Legacy - user has reservation | Blue solid background | ✅ |

#### Props

```typescript
interface StatusButtonProps {
  status: ReservationStatus;
  onReserve?: (e: any) => void;
  onJoinWaitlist?: (e: any) => void;
  onViewReservation?: (e: any) => void;
  onCancelReservation?: (e: any) => void;
  onWalkIn?: (e: any) => void;
}
```

#### Basic Usage

```tsx
import { StatusButton } from './class-button';

// Available class
<StatusButton 
  status="available" 
  onReserve={() => console.log('Reserve pressed')}
/>

// User has reservation
<StatusButton 
  status="cancel_reservation" 
  onCancelReservation={() => console.log('Cancel pressed')}
/>

// Class is full but waitlist available
<StatusButton 
  status="waitlist" 
  onJoinWaitlist={() => console.log('Join waitlist pressed')}
/>

// Disabled states (no handlers needed)
<StatusButton status="class_cancelled" />
<StatusButton status="class_full" />
```

### 2. `ActionButtonDemo` (action-button-demo.tsx)
A comprehensive demo component showing all button states with example usage.

### 3. `EnhancedClassCard` (enhanced-class-card.tsx)
An enhanced version of your class card that demonstrates:
- Dynamic status determination based on class data
- Proper integration of the action button
- Example action handlers with alerts

## Integration Guide

### Step 1: Update Your Class Data Type

Add these optional fields to your class data interface:

```typescript
interface ClassDetailsResponse {
  // ... existing fields
  reservation_status?: 'none' | 'reserved' | 'waitlisted';
  is_cancelled?: boolean;
  is_full?: boolean;
  allows_walk_in?: boolean;
  user_has_reservation?: boolean;
}
```

### Step 2: Implement Status Logic

Create a function to determine the button status:

```typescript
const getButtonStatus = (classData) => {
  if (classData.is_cancelled) return 'class_cancelled';
  if (classData.user_has_reservation) return 'cancel_reservation';
  if (classData.reservation_status === 'reserved') return 'reserved';
  if (classData.reservation_status === 'waitlisted') return 'waitlist';
  if (classData.is_full && classData.allows_walk_in) return 'walk_in_available';
  if (classData.is_full) return 'class_full';
  return 'available';
};
```

### Step 3: Replace Your Current Button

Replace your existing `StatusButton` usage:

```tsx
// Before
<StatusButton status={"available"} />

// After
<StatusButton 
  status={getButtonStatus(classData)}
  onReserve={handleReserve}
  onJoinWaitlist={handleJoinWaitlist}
  onCancelReservation={handleCancelReservation}
  onViewReservation={handleViewReservation}
  onWalkIn={handleWalkIn}
/>
```

### Step 4: Implement Action Handlers

```typescript
const handleReserve = async () => {
  try {
    // Call your reservation API
    await reserveClass(classId);
    // Update UI state
  } catch (error) {
    Alert.alert('Error', 'Failed to reserve class');
  }
};

const handleCancelReservation = async () => {
  Alert.alert(
    'Cancel Reservation',
    'Are you sure you want to cancel?',
    [
      { text: 'Keep Reservation', style: 'cancel' },
      { 
        text: 'Cancel', 
        style: 'destructive', 
        onPress: async () => {
          try {
            await cancelReservation(reservationId);
            // Update UI state
          } catch (error) {
            Alert.alert('Error', 'Failed to cancel reservation');
          }
        }
      }
    ]
  );
};

// Similar implementations for other handlers...
```

## Styling Customization

The button styles are defined in the `getButtonConfig` function. You can customize:

- Background colors (`className`)
- Text colors and fonts (`textClassName`)
- Border styles
- Disabled states

Example customization:

```typescript
available: {
  variant: "outline" as ButtonVariant,
  className: "rounded-2xl bg-[#YOUR_COLOR] border-0",
  textClassName: "text-[#YOUR_TEXT_COLOR] font-dm-sans-medium text-sm",
  text: "Reserve",
  actionType: "reserve" as const,
  disabled: false,
},
```

## Testing

The demo component (`ActionButtonDemo`) provides a comprehensive test environment where you can:
- See all button states visually
- Test interactions with alert dialogs
- Verify styling matches your design

To use the demo:

```tsx
import { ActionButtonDemo } from './action-button-demo';

// In your screen or navigation
<ActionButtonDemo />
```

## Backward Compatibility

The enhanced component maintains backward compatibility with your existing code:
- Old status values (`"cancelled"`, `"reserved"`) still work
- Existing prop names are unchanged
- Default behavior remains the same

## Next Steps

1. **Test the demo component** to see all states in action
2. **Update your class data** to include the new status fields
3. **Implement the action handlers** with your API calls
4. **Replace existing button usage** with the enhanced version
5. **Customize styling** to match your exact design requirements

The enhanced action button provides a flexible, maintainable solution that matches your design mockup while being easy to integrate into your existing codebase.
