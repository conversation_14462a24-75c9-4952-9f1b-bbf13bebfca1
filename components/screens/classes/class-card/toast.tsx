import { CloseIcon, Icon } from "@/components/ui/icon";
import { Pressable } from "@/components/ui/pressable";
import { useToast, Toast, ToastDescription } from "@/components/ui/toast";
import { TriangleAlert } from "lucide-react-native";
import React from "react";

export const useToaster = (id: string) => {
  const toast = useToast();
  const [toastId, setToastId] = React.useState(id);

  const handleToast = (message?: string) => {
    console.log("Showing toast with message:", message);
    console.log("toastId", toast.isActive(toastId));
    // Always show new toast for errors to ensure visibility
    showNewToast(message);
  };
  const showNewToast = (message?: string) => {
    const newId = Math.random().toString();
    setToastId(newId);
    const displayMessage = message || "An error occurred. Please try again.";

    toast.show({
      id: newId,
      placement: "bottom",
      duration: 3000,
      render: ({ id }) => {
        const uniqueToastId = "toast-" + id;
        return (
          <Toast
            className="bg-red-100 flex rounded-2xl flex-row gap-2 p-6 items-center"
            nativeID={uniqueToastId}
            action="error"
            variant="outline"
          >
            <Icon as={TriangleAlert} size="lg" className="text-red-500" />
            <ToastDescription className="font-semibold text-sm text-error-500">
              {displayMessage}
            </ToastDescription>
            <Pressable onPress={() => toast.close(id)}>
              <Icon as={CloseIcon} className="text-red-500" />
            </Pressable>
          </Toast>
        );
      },
    });
  };

  return handleToast;
};
