import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonText } from "@/components/ui/button"; // Adjust import path

type ReservationStatus =
  | "available"
  | "cancelled"
  | "waitlist"
  | "reserved"
  | "class_cancelled"
  | "walk_in_available"
  | "class_full"
  | "cancel_reservation";

interface StatusButtonProps {
  status: ReservationStatus;
  onReserve?: (e: any) => void;
  onJoinWaitlist?: (e: any) => void;
  onViewReservation?: (e: any) => void;
  onCancelReservation?: (e: any) => void;
  onWalkIn?: (e: any) => void;
  className?: string;
  isLoading?: boolean;
}

type ButtonVariant = "outline" | "solid" | "link" | undefined;

// Configuration object for button variants (testable, pure function)
const getButtonConfig = (status: ReservationStatus) => {
  const configs = {
    available: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-[#E6F9FC] border-0",
      textClassName: "text-[#00697B] font-dm-sans-medium text-sm",
      text: "Reserve",
      actionType: "reserve" as const,
      disabled: false,
    },
    cancel_reservation: {
      variant: "outline" as ButtonVariant,
      className: "bg-[#FFF0F0] rounded-2xl border border-[#FF6B6B]",
      textClassName: "text-[#FF6B6B] font-dm-sans-medium text-sm font-bolder",
      text: "Cancel reservation",
      actionType: "cancelReservation" as const,
      disabled: false,
    },
    class_cancelled: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-gray-100 border border-gray-300",
      textClassName: "text-gray-500 font-dm-sans-medium text-sm",
      text: "Class cancelled",
      actionType: null,
      disabled: true,
    },
    waitlist: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-[#F0FDF4] border border-[#22C55E]",
      textClassName: "font-dm-sans-medium text-sm text-[#16A34A]",
      text: "Join waitlist",
      actionType: "waitlist" as const,
      disabled: false,
    },
    walk_in_available: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-white border border-gray-300",
      textClassName: "text-gray-700 font-dm-sans-medium text-sm",
      text: "Walk-in available",
      actionType: "walkIn" as const,
      disabled: false,
    },
    class_full: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-gray-100 border border-gray-300",
      textClassName: "text-gray-500 font-dm-sans-medium text-sm",
      text: "Class full",
      actionType: null,
      disabled: true,
    },
    // Legacy support - keeping old names for backward compatibility
    cancelled: {
      variant: "outline" as ButtonVariant,
      className: "bg-error-50 rounded-2xl border border-error-500",
      textClassName: "text-error-500 font-dm-sans-medium text-sm font-bolder",
      text: "Cancel reservation",
      actionType: "cancelReservation" as const,
      disabled: false,
    },
    reserved: {
      variant: "solid" as ButtonVariant,
      className: "bg-primary-500 border-0 px-4 py-2 rounded-lg",
      textClassName: "text-white font-dm-sans-medium text-sm",
      text: "Reserve",
      actionType: "viewReservation" as const,
      disabled: false,
    },
  };

  return configs[status] || null;
};

// Helper to get the appropriate handler
const getActionHandler = (
  actionType: string | null,
  handlers: Pick<
    StatusButtonProps,
    | "onReserve"
    | "onJoinWaitlist"
    | "onViewReservation"
    | "onCancelReservation"
    | "onWalkIn"
  >
) => {
  const actionMap = {
    reserve: handlers.onReserve,
    waitlist: handlers.onJoinWaitlist,
    viewReservation: handlers.onViewReservation,
    cancelReservation: handlers.onCancelReservation,
    walkIn: handlers.onWalkIn,
  };

  return actionType
    ? actionMap[actionType as keyof typeof actionMap]
    : undefined;
};

// Main component
export const ClassStatusButton: React.FC<StatusButtonProps> = ({
  status = "available",
  onReserve,
  onJoinWaitlist,
  onViewReservation,
  onCancelReservation,
  onWalkIn,
  className,
  isLoading,
}) => {
  const config = getButtonConfig(status);

  if (!config) {
    return null;
  }

  const handler = getActionHandler(config.actionType, {
    onReserve,
    onJoinWaitlist,
    onViewReservation,
    onCancelReservation,
    onWalkIn,
  });

  return (
    <Button
      action="secondary"
      size="sm"
      variant={config.variant}
      className={`${config.className} ${className}`}
      disabled={config.disabled}
      onPress={(e) => {
        e.stopPropagation();
        if (!config.disabled) {
          handler?.(e);
        }
      }}
    >
      {isLoading && <ButtonSpinner color="gray" />}
      <ButtonText className={config.textClassName ?? config.className}>
        {isLoading ? "Loading..." : config.text}
      </ButtonText>
    </Button>
  );
};
