import { useState, useEffect } from "react";
import * as LocalAuthentication from "expo-local-authentication";
import {
  isBiometricEnabled,
  setBiometricEnabled,
  storeBiometricCredentials,
  getBiometricCredentials,
  clearBiometricCredentials,
  encryptPassword,
  BiometricCredentials,
} from "../biometric-storage";

export interface BiometricAuthState {
  isSupported: boolean;
  isEnrolled: boolean;
  isEnabled: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  isLoading: boolean;
}

export interface UseBiometricAuthReturn {
  state: BiometricAuthState;
  authenticate: () => Promise<{
    success: boolean;
    credentials?: BiometricCredentials;
  }>;
  enableBiometric: (email: string, password: string) => Promise<boolean>;
  disableBiometric: () => Promise<boolean>;
  checkBiometricStatus: () => Promise<void>;
}

export const useBiometricAuth = (): UseBiometricAuthReturn => {
  const [state, setState] = useState<BiometricAuthState>({
    isSupported: false,
    isEnrolled: false,
    isEnabled: false,
    supportedTypes: [],
    isLoading: true,
  });

  const checkBiometricStatus = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      // Check if biometric authentication is supported
      const isSupported = await LocalAuthentication.hasHardwareAsync();

      // Check if biometric data is enrolled
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      // Get supported authentication types
      const supportedTypes =
        await LocalAuthentication.supportedAuthenticationTypesAsync();

      // Check if biometric is enabled in our app
      const isEnabled = await isBiometricEnabled();

      setState({
        isSupported,
        isEnrolled,
        isEnabled,
        supportedTypes,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error checking biometric status:", error);
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const authenticate = async (): Promise<{
    success: boolean;
    credentials?: BiometricCredentials;
  }> => {
    try {
      if (!state.isSupported || !state.isEnrolled || !state.isEnabled) {
        return { success: false };
      }

      // Get the biometric type name for the prompt
      const biometricType = getBiometricTypeName(state.supportedTypes);

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: `Use ${biometricType} to sign in`,
        cancelLabel: "Cancel",
        fallbackLabel: "Use Password",
        disableDeviceFallback: false,
      });

      if (result.success) {
        const credentials = await getBiometricCredentials();
        return { success: true, credentials: credentials || undefined };
      }

      return { success: false };
    } catch (error) {
      console.error("Biometric authentication error:", error);
      return { success: false };
    }
  };

  const enableBiometric = async (
    email: string,
    password: string
  ): Promise<boolean> => {
    try {
      if (!state.isSupported || !state.isEnrolled) {
        throw new Error(
          "Biometric authentication is not available on this device"
        );
      }

      // First authenticate with biometrics to confirm user intent
      const biometricType = getBiometricTypeName(state.supportedTypes);

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: `Enable ${biometricType} for quick sign-in`,
        cancelLabel: "Cancel",
        fallbackLabel: "Cancel",
        disableDeviceFallback: true,
      });

      if (result.success) {
        // Store credentials securely
        const encryptedPassword = encryptPassword(password);
        await storeBiometricCredentials({ email, password: encryptedPassword });
        await setBiometricEnabled(true);

        // Update state
        setState((prev) => ({ ...prev, isEnabled: true }));
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error enabling biometric authentication:", error);
      return false;
    }
  };

  const disableBiometric = async (): Promise<boolean> => {
    try {
      await setBiometricEnabled(false);
      setState((prev) => ({ ...prev, isEnabled: false }));
      return true;
    } catch (error) {
      console.error("Error disabling biometric authentication:", error);
      return false;
    }
  };

  useEffect(() => {
    checkBiometricStatus();
  }, []);

  return {
    state,
    authenticate,
    enableBiometric,
    disableBiometric,
    checkBiometricStatus,
  };
};

/**
 * Get a user-friendly name for the biometric authentication type
 */
const getBiometricTypeName = (
  types: LocalAuthentication.AuthenticationType[]
): string => {
  if (
    types.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)
  ) {
    return "Face ID";
  }
  if (types.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
    return "Touch ID";
  }
  if (types.includes(LocalAuthentication.AuthenticationType.IRIS)) {
    return "Iris";
  }
  return "Biometric";
};
