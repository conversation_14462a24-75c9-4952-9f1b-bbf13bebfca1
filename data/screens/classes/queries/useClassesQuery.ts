import { api } from "@/lib/api";

import { useQuery } from "@tanstack/react-query";
import { useSession } from "@/modules/login/auth-provider";
import { ClassDetailsResponse } from "../types";

export const fetchClassesByOrgId = async ({
  orgId,
  date,
}: {
  orgId: string;
  date: string;
}) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: orgId,
      date,
    }).toString();

    const response = await api
      .get<{
        classes: ClassDetailsResponse[];
      }>(
        `classes/reservations/list?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`
      )
      .json();

    return response?.classes;
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};

export const useClassesQuery = ({ date }: { date: string }) => {
  const { data: session } = useSession();

  return useQuery({
    queryKey: ["classes", date],
    queryFn: () =>
      fetchClassesByOrgId({
        date,
        orgId: session?.university_id as string,
      }),
  });
};
