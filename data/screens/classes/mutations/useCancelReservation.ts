import { api } from "@/lib/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const cancelReservation = async (id: number) => {
  try {
    const result = await api
      .post("reservations/cancel", {
        json: {
          reservation_id: id,
        },
      })
      .json();

    return result;
  } catch (err) {
    throw new Error(`${err}`);
  }
};

export const useCancelReservation = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ["classes"],
      });

      if (onSuccess) {
        onSuccess();
      }
    },
  });
};
