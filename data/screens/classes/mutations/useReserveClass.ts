import { useToaster } from "@/components/screens/classes/class-card/toast";
import { formatDate } from "@/data/common/common.utils";
import { api } from "@/lib/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { uniqueId } from "lodash/fp";

interface ReservationEventData {
  class_id: number;
  date: string;
  is_virtual: boolean;
  type?: string;
}

export const reserveAction = async (data: ReservationEventData) => {
  try {
    const result = await api
      .post("reserve", {
        json: {
          ...data,
          date: formatDate(data.date),
          type: "class",
        },
      })
      .json();

    return result;
  } catch (err) {
    // If err is already an Error object with a message, use it directly
    if (err instanceof Error) {
      throw err;
    }
    // Otherwise, create a new Error with the string representation
    throw new Error(String(err));
  }
};

export const useReserveClass = (onSuccess?: () => void) => {
  const toast = useToaster(uniqueId("reserve"));

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ["classes"],
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },

    onError: (e) => {
      console.log("Reservation error:", e);
      const errorMessage = e instanceof Error ? e.message : String(e);
      toast(errorMessage || "Failed to reserve class. Please try again.");
    },
  });
};
