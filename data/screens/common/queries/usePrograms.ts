import { api } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import { ProgramResponse } from "../types";

export const fetchPrograms = async () => {
  try {
    return await api.get<ProgramResponse[]>(`programs`).json();
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const usePrograms = () => {
  return useQuery({
    queryKey: ["programs"],
    queryFn: () => fetchPrograms(),
  });
};
