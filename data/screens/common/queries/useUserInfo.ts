import { api } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import { UserResponse } from "../types";

export const fetchUserInfo = async (id: string) => {
  try {
    const response = await api
      .get<{ data: UserResponse }>(`users/${id}`)
      .json();
    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const useUserInfo = (id: string) => {
  return useQuery({
    queryKey: ["user-info", id],
    queryFn: () => fetchUserInfo(id),
  });
};
