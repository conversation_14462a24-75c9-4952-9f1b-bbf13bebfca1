import { Specialty } from "../appointments/types";

interface SecondaryGym {
  id: number;
  name: string;
}

interface TrainingSession {
  id: number;
  name: string;
  gym_name: string;
}

interface TimeSlot {
  id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
  day_of_week_name: string;
  schedule_id: number;
}

interface ScheduleDetail {
  id: number;
  gym_id: number;
  gym_name: string;
  room_id: number;
  room_name: string;
  active_in_app: number;
  equipment_categories: unknown[]; // kept generic; refine if known
}

interface ScheduleBlock {
  schedule: ScheduleDetail;
  time_slots: TimeSlot[];
}

interface RoleDetails {
  id: number;
  role: string;
  access_level: number;
}

interface ClassInstructorPayroll {
  class_id: number;
  class_name: string;
  class_instructor_id: number;
  class_start_time: string;
  class_end_time: string;
  gym_name: string;
  instructor_first_name: string;
  instructor_last_name: string;
  instructor_email: string;
  pay_amount: number;
  payroll_instructor_id: number;
  is_instructor_for_class: boolean;
}

interface FamilyMember {}

interface Membership {}

// --- MAIN USER TYPE ---
interface User {
  id: number;
  created: string;
  updated: string;
  university_id: number;
  gym_id: number;
  secondary_gym_ids: string;
  primary_role: number;
  account_status: number;
  username: string;
  first_name: string;
  last_name: string;
  profile_image: string | null;
  email: string;
  phone: string;
  sex: string;
  frequency: string | null;
  class: string | null;
  program_preferences: string | null;
  specialties: Specialty[];
  years_experience: number;
  bio: string | null;
  dob: string | null;
  barcode: string | null;
  ymca_id: string | null;
  foreign_id: string | null;
  is_paid: number;
  auto_suspended: number;
  is_trainer: number;
  has_subscription: number;
  deleted: number;
  creation_source: string | null;
  update_source: string | null;
  created_by: number | null;
  last_updated_by: number | null;
  commission_percentage: number;
  is_vaccinated: number;
  vaccination_expires: string | null;
  deleted_by: number | null;
  deleted_on: string | null;
  last_logged_in: string | null;
  active: number;
  gym_name: string;
  role: string;
  secondary_gyms: SecondaryGym[];
  memberships: Membership[];
  training_sessions: TrainingSession[];
  block_schedules: ScheduleBlock[]; // assuming similar shape, adjust if needed
  schedule: ScheduleBlock[];
  certifications: unknown[];
  availability_schedule: unknown[];
  roleDetails: RoleDetails;
  class_instructor_payroll: ClassInstructorPayroll[];
  family_members: FamilyMember[];
}

export interface UserResponse {
  user: User;
  memberships: Membership[];
}

export interface ProgramResponse {
  id: number | string;
  university_id: number;
  gym_id: number;
  title: string;
  text: string | null;
  url: string | null;
  icon: string;
  all_locations: number;
  created: string;
  active: number;
  gym_name: string | null;
}
