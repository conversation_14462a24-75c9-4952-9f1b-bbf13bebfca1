export interface AppointmentType {
  id: number;
  gym_name: string;
  name: string;
  reservations_count: number;
  room_name: string;
}

export interface AppointmentCard {
  id: number;
  data: AppointmentType;
  trainers: Trainer[];
}

export interface Specialty {
  id: number;
  name: string;
}

export interface Trainer {
  id: number;
  gym_id: number;
  first_name: string;
  last_name: string;
  email: string;
  gender: string;
  years_experience: number;
  specialties: Specialty[];
  bio: string | null;
  primary_role: number;
  profile_image: string | null;
  gym_name: string;
  user_images: string[];
}
