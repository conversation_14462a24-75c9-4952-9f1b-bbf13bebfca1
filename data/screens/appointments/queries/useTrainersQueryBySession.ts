import { api } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";

import { Trainer } from "../types";

export const fetchTrainersBySessionId = async (id: string) => {
  try {
    const response = await api
      .get<{
        data: Trainer[];
      }>(`users/trainers?session_id=${id}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const useTrainersQueryBySession = (id: string) => {
  return useQuery({
    queryKey: ["trainers", id],
    queryFn: () => fetchTrainersBySessionId(id),
  });
};
