import { api } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";

import { AppointmentType } from "../types";

export const fetchAppointments = async ({ date }: { date: string }) => {
  try {
    const urlParams = new URLSearchParams({
      date,
    }).toString();

    const response = await api
      .get<{
        data: AppointmentType[];
      }>(`pt/appointments?${urlParams}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const useAppointmentsQuery = ({ date }: { date: string }) => {
  return useQuery({
    queryKey: ["appointments", date],
    queryFn: () =>
      fetchAppointments({
        date,
      }),
  });
};
